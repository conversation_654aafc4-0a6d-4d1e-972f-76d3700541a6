/**
 * Advanced Patch System
 *
 * Provides intelligent patch generation, application, and management
 * Supports unified diff format, conflict resolution, and rollback
 */
export interface PatchOperation {
    type: 'add' | 'remove' | 'modify';
    file: string;
    lineNumber: number;
    content: string;
    originalContent?: string;
}
export interface Patch {
    id: string;
    timestamp: string;
    description: string;
    operations: PatchOperation[];
    checksum: string;
    metadata: {
        author?: string;
        version?: string;
        dependencies?: string[];
    };
}
export interface PatchResult {
    success: boolean;
    appliedOperations: number;
    failedOperations: PatchOperation[];
    conflicts: PatchConflict[];
    warnings: string[];
}
export interface PatchConflict {
    file: string;
    lineNumber: number;
    expected: string;
    actual: string;
    operation: PatchOperation;
}
/**
 * Patch manager for handling file modifications
 */
export declare class PatchManager {
    private appliedPatches;
    private backupDirectory;
    constructor(backupDirectory?: string);
    /**
     * Generate patch from file differences
     */
    generatePatch(originalFile: string, modifiedFile: string, description?: string): Promise<Patch>;
    /**
     * Apply patch to files
     */
    applyPatch(patch: Patch, dryRun?: boolean): Promise<PatchResult>;
    /**
     * Apply single operation
     */
    private applyOperation;
    /**
     * Rollback patch
     */
    rollbackPatch(patchId: string): Promise<boolean>;
    /**
     * Convert diff to operations
     */
    private diffToOperations;
    /**
     * Calculate patch checksum
     */
    private calculateChecksum;
    /**
     * Generate unique patch ID
     */
    private generatePatchId;
    /**
     * Backup file before modification
     */
    private backupFile;
    /**
     * Restore file from backup
     */
    private restoreFile;
    /**
     * Ensure backup directory exists
     */
    private ensureBackupDirectory;
    /**
     * Save patch record
     */
    private savePatchRecord;
    /**
     * Remove patch record
     */
    private removePatchRecord;
}
export declare const patchManager: PatchManager;
//# sourceMappingURL=patch-system.d.ts.map