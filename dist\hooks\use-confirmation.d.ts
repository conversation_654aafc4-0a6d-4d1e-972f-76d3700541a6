/**
 * Confirmation Queue Management Hook
 *
 * Manages multiple confirmation requests with promise-based API
 * Provides queue-based system for handling confirmations
 */
import React from 'react';
export interface ConfirmationResult {
    confirmed: boolean;
    value?: string;
    action?: string;
}
/**
 * Custom hook for managing confirmation dialogs
 */
export declare function useConfirmation(): {
    submitConfirmation: (result: ConfirmationResult) => void;
    requestConfirmation: (prompt: React.ReactElement, explanation?: string) => Promise<ConfirmationResult>;
    confirmationPrompt: React.ReactElement | null;
    explanation?: string;
    isConfirmationPending: boolean;
};
//# sourceMappingURL=use-confirmation.d.ts.map