{"version": 3, "file": "diff-overlay.js", "sourceRoot": "", "sources": ["../../../src/components/overlays/diff-overlay.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC1C,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAgB9D,MAAM,UAAU,WAAW,CAAC,EAC1B,OAAO,EACP,OAAO,EACP,gBAAgB,GAAG,OAAO,CAAC,GAAG,EAAE,EACf;IACjB,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAS,EAAE,CAAC,CAAC;IAC3D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAa,EAAE,CAAC,CAAC;IAC3D,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC9C,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpD,MAAM,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC9D,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAqB,SAAS,CAAC,CAAC;IAExE,yCAAyC;IACzC,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,OAAO,EAAE,CAAC;YACZ,QAAQ,EAAE,CAAC;QACb,CAAC;IACH,CAAC,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEhC,wBAAwB;IACxB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,gBAAgB;QAChB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QAED,aAAa;QACb,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC3B,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC;iBAAM,CAAC;gBACN,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC3B,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,CAAC;iBAAM,CAAC;gBACN,eAAe,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACpC,CAAC;YACD,OAAO;QACT,CAAC;QAED,kBAAkB;QAClB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,EAAE,CAAC,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;YACjB,eAAe,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;YACnC,OAAO;QACT,CAAC;QAED,mBAAmB;QACnB,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,WAAW,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACzD,eAAe,CAAC,CAAC,CAAC,CAAC;YACnB,OAAO;QACT,CAAC;QAED,eAAe;QACf,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACnC,QAAQ,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,qBAAqB;QACrB,IAAI,GAAG,CAAC,MAAM,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjE,WAAW,CAAC,MAAM,CAAC,CAAC;YACpB,eAAe,CAAC,CAAC,CAAC,CAAC;YACnB,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACtC,UAAU,CAAC,IAAI,CAAC,CAAC;QACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,OAAO,CAAC,kBAAkB,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAElD,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAEtD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,QAAQ,CAAC,UAAU,CAAC,KAAK,IAAI,wBAAwB,CAAC,CAAC;gBACvD,OAAO;YACT,CAAC;YAED,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEhC,wBAAwB;YACxB,MAAM,KAAK,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAChD,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,oBAAoB,CAAC,CAAC,CAAC,CAAC;YAExB,OAAO,CAAC,qBAAqB,KAAK,CAAC,MAAM,gBAAgB,CAAC,CAAC;QAE7D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB,CAAC;YACpF,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,QAAQ,CAAC,yBAAyB,EAAE,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;QAC5F,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAEvB;;OAEG;IACH,MAAM,gBAAgB,GAAG,CAAC,IAAY,EAAc,EAAE;QACpD,MAAM,KAAK,GAAe,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,WAAW,GAA6B,IAAI,CAAC;QACjD,IAAI,cAAc,GAAa,EAAE,CAAC;QAElC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,cAAc;YACd,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAClC,qBAAqB;gBACrB,IAAI,WAAW,EAAE,CAAC;oBAChB,KAAK,CAAC,IAAI,CAAC;wBACT,GAAG,WAAW;wBACd,OAAO,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;qBACvB,CAAC,CAAC;gBACjB,CAAC;gBAED,iBAAiB;gBACjB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBACvD,IAAI,KAAK,EAAE,CAAC;oBACV,WAAW,GAAG;wBACZ,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACd,MAAM,EAAE,UAAU;wBAClB,SAAS,EAAE,CAAC;wBACZ,SAAS,EAAE,CAAC;qBACb,CAAC;oBACF,cAAc,GAAG,EAAE,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,cAAc;iBACT,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC1C,IAAI,WAAW;oBAAE,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC;YAChD,CAAC;iBACI,IAAI,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC9C,IAAI,WAAW;oBAAE,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;YAClD,CAAC;iBACI,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBACxC,IAAI,WAAW;oBAAE,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;YAClD,CAAC;YAED,4BAA4B;iBACvB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzD,IAAI,WAAW;oBAAE,WAAW,CAAC,SAAS,GAAG,CAAC,WAAW,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5E,CAAC;iBACI,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzD,IAAI,WAAW;oBAAE,WAAW,CAAC,SAAS,GAAG,CAAC,WAAW,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5E,CAAC;YAED,sBAAsB;YACtB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC;QAED,iBAAiB;QACjB,IAAI,WAAW,EAAE,CAAC;YAChB,KAAK,CAAC,IAAI,CAAC;gBACT,GAAG,WAAW;gBACd,OAAO,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;aACvB,CAAC,CAAC;QACjB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF;;OAEG;IACH,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO,CACL,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,YACzB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,oCAA2B,GACzC,CACP,CAAC;QACJ,CAAC;QAED,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,sCACL,SAAS,CAAC,MAAM,UAC3B,EACP,KAAC,IAAI,KAAQ,EAEZ,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;oBAC7B,MAAM,UAAU,GAAG,KAAK,KAAK,iBAAiB,CAAC;oBAC/C,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAChD,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAE9C,OAAO,CACL,KAAC,GAAG,cACF,MAAC,IAAI,IACH,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,EACzC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAChD,IAAI,EAAE,UAAU,aAEf,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACxB,UAAU,OAAG,IAAI,CAAC,IAAI,EACtB,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CACrB,MAAC,IAAI,IAAC,KAAK,EAAC,OAAO,mBAAI,IAAI,CAAC,SAAS,IAAQ,CAC9C,EACA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CACrB,MAAC,IAAI,IAAC,KAAK,EAAC,KAAK,mBAAI,IAAI,CAAC,SAAS,IAAQ,CAC5C,IACI,IAdC,IAAI,CAAC,IAAI,CAeb,CACP,CAAC;gBACJ,CAAC,CAAC,IACE,CACP,CAAC;IACJ,CAAC,CAAC;IAEF;;OAEG;IACH,MAAM,cAAc,GAAG,GAAG,EAAE;QAC1B,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,CACL,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,YACzB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,0CAAiC,GAC/C,CACP,CAAC;QACJ,CAAC;QAED,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACtC,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE,YAAY,GAAG,EAAE,CAAC,CAAC;QAElE,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACxB,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;oBAChC,IAAI,KAAK,GAAG,OAAO,CAAC;oBAEpB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBACzB,KAAK,GAAG,OAAO,CAAC;oBAClB,CAAC;yBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBAChC,KAAK,GAAG,KAAK,CAAC;oBAChB,CAAC;yBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;wBACjC,KAAK,GAAG,MAAM,CAAC;oBACjB,CAAC;yBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;wBACzC,KAAK,GAAG,QAAQ,CAAC;oBACnB,CAAC;oBAED,OAAO,CACL,KAAC,IAAI,IAAa,KAAK,EAAE,KAAK,YAC3B,IAAI,IADI,KAAK,CAET,CACR,CAAC;gBACJ,CAAC,CAAC,EAED,YAAY,GAAG,EAAE,GAAG,KAAK,CAAC,MAAM,IAAI,CACnC,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,2BACpB,KAAK,CAAC,MAAM,GAAG,YAAY,GAAG,EAAE,mBAChC,CACR,IACG,CACP,CAAC;IACJ,CAAC,CAAC;IAEF;;OAEG;IACH,MAAM,cAAc,GAAG,CAAC,MAAc,EAAU,EAAE;QAChD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,OAAO,CAAC,CAAC,OAAO,OAAO,CAAC;YAC7B,KAAK,SAAS,CAAC,CAAC,OAAO,KAAK,CAAC;YAC7B,KAAK,UAAU,CAAC,CAAC,OAAO,QAAQ,CAAC;YACjC,KAAK,SAAS,CAAC,CAAC,OAAO,MAAM,CAAC;YAC9B,OAAO,CAAC,CAAC,OAAO,OAAO,CAAC;QAC1B,CAAC;IACH,CAAC,CAAC;IAEF;;OAEG;IACH,MAAM,aAAa,GAAG,CAAC,MAAc,EAAU,EAAE;QAC/C,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC;YACzB,KAAK,SAAS,CAAC,CAAC,OAAO,GAAG,CAAC;YAC3B,KAAK,UAAU,CAAC,CAAC,OAAO,GAAG,CAAC;YAC5B,KAAK,SAAS,CAAC,CAAC,OAAO,GAAG,CAAC;YAC3B,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC;QACtB,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,MAAC,GAAG,IACF,QAAQ,EAAC,UAAU,EACnB,WAAW,EAAC,QAAQ,EACpB,WAAW,EAAC,MAAM,EAClB,aAAa,EAAC,QAAQ,EACtB,KAAK,EAAC,KAAK,EACX,MAAM,EAAC,KAAK,aAGZ,MAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,aACpE,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,kCACT,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,IACvD,EACP,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,mGAEX,GACH,IACF,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,YACvC,OAAO,CAAC,CAAC,CAAC,CACT,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,oCAA2B,CAChD,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CACV,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,MAAC,IAAI,IAAC,KAAK,EAAC,KAAK,wBAAS,KAAK,IAAQ,EACvC,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,iCAAwB,IACtC,CACP,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,CAC3B,aAAa,EAAE,CAChB,CAAC,CAAC,CAAC,CACF,cAAc,EAAE,CACjB,GACG,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,YACpE,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,oCACI,gBAAgB,IAC/B,GACH,IACF,CACP,CAAC;AACJ,CAAC"}