/**
 * Comprehensive Tool Registry System
 * 
 * Manages all available tools, their execution, and integration
 * Provides tool discovery, validation, and execution framework
 */

import { sandboxExecutor, ExecutionResult } from '../sandbox-execution.js';
import { approvalManager, requestCommandApproval } from '../security/approval-system.js';
import { logInfo, logError, logWarning } from '../logger/log.js';

export interface Tool {
  id: string;
  name: string;
  description: string;
  category: 'file' | 'git' | 'system' | 'network' | 'analysis' | 'development';
  version: string;
  parameters: ToolParameter[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  requiresApproval: boolean;
  execute: (params: Record<string, any>, context: ToolContext) => Promise<ToolResult>;
}

export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  default?: any;
  validation?: (value: any) => boolean;
}

export interface ToolContext {
  workingDirectory: string;
  userId?: string;
  sessionId?: string;
  environment: Record<string, string>;
  capabilities: string[];
}

export interface ToolResult {
  success: boolean;
  data?: any;
  output?: string;
  error?: string;
  metadata?: Record<string, any>;
  executionTime: number;
}

/**
 * Tool registry and execution manager
 */
export class ToolRegistry {
  private tools = new Map<string, Tool>();
  private executionHistory: ToolExecution[] = [];

  constructor() {
    this.registerBuiltinTools();
  }

  /**
   * Register a tool
   */
  registerTool(tool: Tool): void {
    this.tools.set(tool.id, tool);
    logInfo(`Registered tool: ${tool.name} (${tool.id})`);
  }

  /**
   * Get tool by ID
   */
  getTool(id: string): Tool | undefined {
    return this.tools.get(id);
  }

  /**
   * Get all tools
   */
  getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  /**
   * Get tools by category
   */
  getToolsByCategory(category: Tool['category']): Tool[] {
    return Array.from(this.tools.values()).filter(tool => tool.category === category);
  }

  /**
   * Execute tool with approval and security checks
   */
  async executeTool(
    toolId: string,
    parameters: Record<string, any>,
    context: ToolContext
  ): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      const tool = this.getTool(toolId);
      if (!tool) {
        throw new Error(`Tool ${toolId} not found`);
      }

      // Validate parameters
      const validationResult = this.validateParameters(tool, parameters);
      if (!validationResult.valid) {
        throw new Error(`Parameter validation failed: ${validationResult.errors.join(', ')}`);
      }

      // Check if approval is required
      if (tool.requiresApproval) {
        const approval = await requestCommandApproval(
          `tool:${tool.id}`,
          Object.keys(parameters),
          `Execute tool: ${tool.name}`
        );

        if (approval.status !== 'approved') {
          throw new Error(`Tool execution not approved: ${approval.status}`);
        }
      }

      logInfo(`Executing tool: ${tool.name} with parameters:`, parameters);

      // Execute tool
      const result = await tool.execute(parameters, context);
      const executionTime = Date.now() - startTime;

      // Record execution
      const execution: ToolExecution = {
        toolId: tool.id,
        parameters,
        result,
        context,
        timestamp: new Date().toISOString(),
        executionTime
      };
      this.executionHistory.push(execution);

      logInfo(`Tool ${tool.name} executed successfully in ${executionTime}ms`);
      return { ...result, executionTime };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      logError(`Tool execution failed: ${errorMessage}`);
      
      return {
        success: false,
        error: errorMessage,
        executionTime
      };
    }
  }

  /**
   * Validate tool parameters
   */
  private validateParameters(tool: Tool, parameters: Record<string, any>): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check required parameters
    for (const param of tool.parameters) {
      if (param.required && !(param.name in parameters)) {
        errors.push(`Missing required parameter: ${param.name}`);
        continue;
      }

      const value = parameters[param.name];
      if (value === undefined || value === null) {
        if (param.required) {
          errors.push(`Parameter ${param.name} cannot be null or undefined`);
        }
        continue;
      }

      // Type validation
      if (!this.validateParameterType(value, param.type)) {
        errors.push(`Parameter ${param.name} must be of type ${param.type}`);
        continue;
      }

      // Custom validation
      if (param.validation && !param.validation(value)) {
        errors.push(`Parameter ${param.name} failed custom validation`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate parameter type
   */
  private validateParameterType(value: any, type: ToolParameter['type']): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return false;
    }
  }

  /**
   * Register built-in tools
   */
  private registerBuiltinTools(): void {
    // File operations
    this.registerTool({
      id: 'file_read',
      name: 'Read File',
      description: 'Read contents of a file',
      category: 'file',
      version: '1.0.0',
      riskLevel: 'low',
      requiresApproval: false,
      parameters: [
        {
          name: 'path',
          type: 'string',
          description: 'Path to the file to read',
          required: true
        }
      ],
      execute: async (params) => {
        const fs = await import('fs/promises');
        try {
          const content = await fs.readFile(params.path, 'utf-8');
          return {
            success: true,
            data: content,
            output: `File read successfully: ${params.path}`,
            executionTime: 0
          };
        } catch (error) {
          throw new Error(`Failed to read file: ${error}`);
        }
      }
    });

    this.registerTool({
      id: 'file_write',
      name: 'Write File',
      description: 'Write content to a file',
      category: 'file',
      version: '1.0.0',
      riskLevel: 'medium',
      requiresApproval: true,
      parameters: [
        {
          name: 'path',
          type: 'string',
          description: 'Path to the file to write',
          required: true
        },
        {
          name: 'content',
          type: 'string',
          description: 'Content to write to the file',
          required: true
        }
      ],
      execute: async (params) => {
        const fs = await import('fs/promises');
        try {
          await fs.writeFile(params.path, params.content, 'utf-8');
          return {
            success: true,
            output: `File written successfully: ${params.path}`,
            executionTime: 0
          };
        } catch (error) {
          throw new Error(`Failed to write file: ${error}`);
        }
      }
    });

    // Command execution
    this.registerTool({
      id: 'execute_command',
      name: 'Execute Command',
      description: 'Execute a shell command',
      category: 'system',
      version: '1.0.0',
      riskLevel: 'high',
      requiresApproval: true,
      parameters: [
        {
          name: 'command',
          type: 'string',
          description: 'Command to execute',
          required: true
        },
        {
          name: 'args',
          type: 'array',
          description: 'Command arguments',
          required: false,
          default: []
        },
        {
          name: 'timeout',
          type: 'number',
          description: 'Timeout in milliseconds',
          required: false,
          default: 30000
        }
      ],
      execute: async (params, context) => {
        const result = await sandboxExecutor.executeCommand(
          params.command,
          params.args || [],
          {
            timeout: params.timeout || 30000,
            workingDirectory: context.workingDirectory
          }
        );

        return {
          success: result.success,
          data: {
            stdout: result.stdout,
            stderr: result.stderr,
            exitCode: result.exitCode
          },
          output: result.stdout,
          error: result.success ? undefined : result.stderr,
          executionTime: 0
        };
      }
    });

    // Git operations
    this.registerTool({
      id: 'git_status',
      name: 'Git Status',
      description: 'Get git repository status',
      category: 'git',
      version: '1.0.0',
      riskLevel: 'low',
      requiresApproval: false,
      parameters: [],
      execute: async (params, context) => {
        const { getGitStatus } = await import('../check-in-git.js');
        const status = getGitStatus();
        
        return {
          success: true,
          data: status,
          output: `Git status retrieved for ${context.workingDirectory}`,
          executionTime: 0
        };
      }
    });

    // Analysis tools
    this.registerTool({
      id: 'analyze_project',
      name: 'Analyze Project',
      description: 'Perform full project context analysis',
      category: 'analysis',
      version: '1.0.0',
      riskLevel: 'low',
      requiresApproval: false,
      parameters: [
        {
          name: 'includeContent',
          type: 'boolean',
          description: 'Include file contents in analysis',
          required: false,
          default: false
        }
      ],
      execute: async (params, context) => {
        const { getProjectContext } = await import('../full-context.js');
        const projectContext = await getProjectContext(context.workingDirectory);
        
        return {
          success: true,
          data: projectContext,
          output: `Project analysis complete: ${projectContext.files.length} files analyzed`,
          executionTime: 0
        };
      }
    });

    logInfo(`Registered ${this.tools.size} built-in tools`);
  }

  /**
   * Get execution history
   */
  getExecutionHistory(): ToolExecution[] {
    return [...this.executionHistory];
  }

  /**
   * Clear execution history
   */
  clearExecutionHistory(): void {
    this.executionHistory = [];
  }

  /**
   * Get tool usage statistics
   */
  getUsageStatistics(): Record<string, number> {
    const stats: Record<string, number> = {};
    
    for (const execution of this.executionHistory) {
      stats[execution.toolId] = (stats[execution.toolId] || 0) + 1;
    }
    
    return stats;
  }
}

interface ToolExecution {
  toolId: string;
  parameters: Record<string, any>;
  result: ToolResult;
  context: ToolContext;
  timestamp: string;
  executionTime: number;
}

// Global tool registry instance
export const toolRegistry = new ToolRegistry();

/**
 * Execute tool by ID
 */
export async function executeTool(
  toolId: string,
  parameters: Record<string, any> = {},
  context?: Partial<ToolContext>
): Promise<ToolResult> {
  const defaultContext: ToolContext = {
    workingDirectory: process.cwd(),
    environment: process.env as Record<string, string>,
    capabilities: ['file_read', 'file_write', 'command_execute'],
    ...context
  };

  return toolRegistry.executeTool(toolId, parameters, defaultContext);
}

/**
 * Get available tools
 */
export function getAvailableTools(): Tool[] {
  return toolRegistry.getAllTools();
}
