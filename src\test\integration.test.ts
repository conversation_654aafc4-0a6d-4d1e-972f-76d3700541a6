/**
 * Integration Tests
 * 
 * Comprehensive tests to ensure all components work together seamlessly
 * Tests cross-component connectivity and full system integration
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { sandboxExecutor } from '../utils/sandbox-execution.js';
import { approvalManager } from '../utils/security/approval-system.js';
import { toolRegistry } from '../utils/tools/tool-registry.js';
import { fullContextAnalyzer } from '../utils/full-context.js';
import { patchManager } from '../utils/patch-system.js';
import { autoCompleteManager } from '../utils/autocomplete.js';
import { AgentLoop } from '../utils/agent/agent-loop.js';
import { promises as fs } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';

describe('Integration Tests', () => {
  let testDir: string;

  beforeEach(async () => {
    // Create temporary test directory
    testDir = join(tmpdir(), `test-${Date.now()}`);
    await fs.mkdir(testDir, { recursive: true });
    process.chdir(testDir);
  });

  afterEach(async () => {
    // Cleanup
    await sandboxExecutor.cleanup();
    try {
      await fs.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Sandbox Execution Integration', () => {
    it('should execute JavaScript code in sandbox', async () => {
      const code = `
        console.log('Hello from sandbox!');
        console.log('Current directory:', process.cwd());
      `;

      const result = await sandboxExecutor.executeJavaScript(code);
      
      expect(result.success).toBe(true);
      expect(result.stdout).toContain('Hello from sandbox!');
      expect(result.duration).toBeGreaterThan(0);
    });

    it('should handle timeout correctly', async () => {
      const code = `
        // Infinite loop to test timeout
        while(true) {
          // Do nothing
        }
      `;

      const result = await sandboxExecutor.executeJavaScript(code, {
        timeout: 1000 // 1 second timeout
      });
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('timed out');
    });
  });

  describe('Security & Approval Integration', () => {
    it('should create and approve security actions', async () => {
      const action = approvalManager.createSecurityAction(
        'file_write',
        'Test file write',
        join(testDir, 'test.txt'),
        { content: 'test content' }
      );

      expect(action.riskLevel).toBe('low');
      expect(action.type).toBe('file_write');

      const request = await approvalManager.requestApproval(action);
      expect(request.status).toBe('approved'); // Should auto-approve low risk
    });

    it('should handle high-risk actions correctly', async () => {
      const action = approvalManager.createSecurityAction(
        'command_execute',
        'Delete system files',
        'rm -rf /',
        {}
      );

      expect(action.riskLevel).toBe('critical');

      const request = await approvalManager.requestApproval(action);
      expect(request.status).toBe('pending'); // Should require approval
    });
  });

  describe('Tool Registry Integration', () => {
    it('should execute file read tool', async () => {
      // Create test file
      const testFile = join(testDir, 'test.txt');
      await fs.writeFile(testFile, 'Hello, World!');

      const result = await toolRegistry.executeTool(
        'file_read',
        { path: testFile },
        { workingDirectory: testDir, environment: {}, capabilities: [] }
      );

      expect(result.success).toBe(true);
      expect(result.data).toBe('Hello, World!');
    });

    it('should execute command tool with approval', async () => {
      const result = await toolRegistry.executeTool(
        'execute_command',
        { command: 'echo', args: ['Hello from command!'] },
        { workingDirectory: testDir, environment: {}, capabilities: [] }
      );

      expect(result.success).toBe(true);
      expect(result.data.stdout).toContain('Hello from command!');
    });
  });

  describe('Full Context Analysis Integration', () => {
    it('should analyze project structure', async () => {
      // Create test project structure
      await fs.mkdir(join(testDir, 'src'), { recursive: true });
      await fs.writeFile(join(testDir, 'package.json'), JSON.stringify({
        name: 'test-project',
        dependencies: { react: '^18.0.0' }
      }));
      await fs.writeFile(join(testDir, 'src', 'index.js'), `
        import React from 'react';
        export function App() {
          return <div>Hello World</div>;
        }
      `);

      const context = await fullContextAnalyzer.analyzeProject(testDir);

      expect(context.files.length).toBeGreaterThan(0);
      expect(context.structure.directories).toContain('src');
      expect(context.metadata.languages).toContain('javascript');
      expect(context.metadata.frameworks).toContain('react');
    });
  });

  describe('Patch System Integration', () => {
    it('should generate and apply patches', async () => {
      // Create original file
      const originalFile = join(testDir, 'original.txt');
      const modifiedFile = join(testDir, 'modified.txt');
      
      await fs.writeFile(originalFile, 'Line 1\nLine 2\nLine 3');
      await fs.writeFile(modifiedFile, 'Line 1\nModified Line 2\nLine 3\nNew Line 4');

      // Generate patch
      const patch = await patchManager.generatePatch(
        originalFile,
        modifiedFile,
        'Test modification'
      );

      expect(patch.operations.length).toBeGreaterThan(0);
      expect(patch.checksum).toBeDefined();

      // Apply patch to original file
      const result = await patchManager.applyPatch(patch);

      expect(result.success).toBe(true);
      expect(result.appliedOperations).toBe(patch.operations.length);

      // Verify file was modified correctly
      const finalContent = await fs.readFile(originalFile, 'utf-8');
      const expectedContent = await fs.readFile(modifiedFile, 'utf-8');
      expect(finalContent).toBe(expectedContent);
    });
  });

  describe('Autocomplete Integration', () => {
    it('should provide file completions', async () => {
      // Create test files
      await fs.writeFile(join(testDir, 'test1.js'), 'console.log("test1");');
      await fs.writeFile(join(testDir, 'test2.ts'), 'console.log("test2");');
      await fs.mkdir(join(testDir, 'testdir'));

      const completions = autoCompleteManager.getCompletions('test', 4);

      expect(completions.suggestions.length).toBeGreaterThan(0);
      expect(completions.suggestions.some(s => s.includes('test1.js'))).toBe(true);
      expect(completions.suggestions.some(s => s.includes('test2.ts'))).toBe(true);
    });

    it('should provide slash command completions', async () => {
      const completions = autoCompleteManager.getCompletions('/he', 3);

      expect(completions.type).toBe('slash');
      expect(completions.suggestions.some(s => s.includes('/help'))).toBe(true);
    });
  });

  describe('Agent Loop Integration', () => {
    it('should initialize agent loop with configuration', async () => {
      const agentLoop = new AgentLoop({
        model: 'gpt-4',
        provider: 'openai',
        approvalPolicy: 'suggest',
        additionalWritableRoots: [testDir]
      });

      expect(agentLoop).toBeDefined();
      
      // Test configuration update
      agentLoop.updateConfig({
        model: 'gpt-3.5-turbo',
        approvalPolicy: 'auto-edit'
      });

      // Agent loop should handle configuration changes gracefully
      expect(agentLoop).toBeDefined();
    });
  });

  describe('Cross-Component Integration', () => {
    it('should integrate sandbox execution with approval system', async () => {
      // Create a command that requires approval
      const action = approvalManager.createSecurityAction(
        'command_execute',
        'Test command execution',
        'echo "test"',
        {}
      );

      const request = await approvalManager.requestApproval(action);
      
      if (request.status === 'approved') {
        const result = await sandboxExecutor.executeCommand('echo', ['test']);
        expect(result.success).toBe(true);
        expect(result.stdout).toContain('test');
      }
    });

    it('should integrate tool registry with security system', async () => {
      // Test file write tool with security approval
      const testFile = join(testDir, 'secure-test.txt');
      
      const result = await toolRegistry.executeTool(
        'file_write',
        { path: testFile, content: 'Secure content' },
        { workingDirectory: testDir, environment: {}, capabilities: [] }
      );

      expect(result.success).toBe(true);
      
      // Verify file was created
      const content = await fs.readFile(testFile, 'utf-8');
      expect(content).toBe('Secure content');
    });

    it('should integrate full context with patch system', async () => {
      // Create a project structure
      await fs.mkdir(join(testDir, 'src'));
      await fs.writeFile(join(testDir, 'src', 'main.js'), 'console.log("original");');

      // Analyze project
      const context = await fullContextAnalyzer.analyzeProject(testDir);
      expect(context.files.length).toBeGreaterThan(0);

      // Create modified version
      await fs.writeFile(join(testDir, 'src', 'main-modified.js'), 'console.log("modified");');

      // Generate patch
      const patch = await patchManager.generatePatch(
        join(testDir, 'src', 'main.js'),
        join(testDir, 'src', 'main-modified.js'),
        'Update main.js'
      );

      expect(patch.operations.length).toBeGreaterThan(0);

      // Apply patch
      const result = await patchManager.applyPatch(patch);
      expect(result.success).toBe(true);
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle cascading errors gracefully', async () => {
      // Test error propagation through multiple systems
      try {
        // Try to execute a non-existent tool
        const result = await toolRegistry.executeTool(
          'non_existent_tool',
          {},
          { workingDirectory: testDir, environment: {}, capabilities: [] }
        );

        expect(result.success).toBe(false);
        expect(result.error).toContain('not found');
      } catch (error) {
        // Should not throw, should return error result
        expect(true).toBe(false);
      }
    });

    it('should handle sandbox execution errors', async () => {
      const result = await sandboxExecutor.executeCommand('non-existent-command');
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Performance Integration', () => {
    it('should handle concurrent operations', async () => {
      const operations = Array.from({ length: 5 }, (_, i) => 
        sandboxExecutor.executeCommand('echo', [`test-${i}`])
      );

      const results = await Promise.all(operations);
      
      expect(results.length).toBe(5);
      results.forEach((result, i) => {
        expect(result.success).toBe(true);
        expect(result.stdout).toContain(`test-${i}`);
      });
    });

    it('should handle resource cleanup', async () => {
      // Create multiple sandbox environments
      const environments = await Promise.all([
        sandboxExecutor.createEnvironment(),
        sandboxExecutor.createEnvironment(),
        sandboxExecutor.createEnvironment()
      ]);

      expect(environments.length).toBe(3);
      expect(sandboxExecutor.getEnvironmentCount()).toBe(3);

      // Cleanup all environments
      await sandboxExecutor.cleanup();
      expect(sandboxExecutor.getEnvironmentCount()).toBe(0);
    });
  });
});
