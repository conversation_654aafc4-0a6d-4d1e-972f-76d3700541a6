/**
 * Security & Approval System
 *
 * Provides comprehensive security controls and approval workflows
 * Includes risk assessment, user confirmation, and audit logging
 */
export interface SecurityAction {
    id: string;
    type: 'file_write' | 'file_delete' | 'command_execute' | 'network_request' | 'system_modify';
    description: string;
    target: string;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    metadata: Record<string, any>;
    timestamp: string;
}
export interface ApprovalRequest {
    id: string;
    action: SecurityAction;
    requiredApprovals: number;
    approvals: Approval[];
    status: 'pending' | 'approved' | 'rejected' | 'expired';
    expiresAt: string;
    createdAt: string;
}
export interface Approval {
    userId: string;
    decision: 'approve' | 'reject';
    reason?: string;
    timestamp: string;
}
export interface SecurityPolicy {
    autoApprove: {
        lowRisk: boolean;
        mediumRisk: boolean;
        highRisk: boolean;
        criticalRisk: boolean;
    };
    requiredApprovals: {
        low: number;
        medium: number;
        high: number;
        critical: number;
    };
    approvalTimeout: number;
    auditLogging: boolean;
    restrictedPaths: string[];
    allowedCommands: string[];
    blockedCommands: string[];
}
/**
 * Security and approval manager
 */
export declare class ApprovalManager {
    private pendingRequests;
    private auditLog;
    private policy;
    constructor(policy?: Partial<SecurityPolicy>);
    /**
     * Request approval for security action
     */
    requestApproval(action: SecurityAction): Promise<ApprovalRequest>;
    /**
     * Provide approval for a request
     */
    provideApproval(requestId: string, userId: string, decision: 'approve' | 'reject', reason?: string): Promise<boolean>;
    /**
     * Assess risk level for an action
     */
    assessRisk(action: Omit<SecurityAction, 'id' | 'riskLevel' | 'timestamp'>): 'low' | 'medium' | 'high' | 'critical';
    /**
     * Create security action with risk assessment
     */
    createSecurityAction(type: SecurityAction['type'], description: string, target: string, metadata?: Record<string, any>): SecurityAction;
    /**
     * Check if path is restricted
     */
    private isRestrictedPath;
    /**
     * Check if action should be auto-approved
     */
    private shouldAutoApprove;
    /**
     * Generate unique request ID
     */
    private generateRequestId;
    /**
     * Generate unique action ID
     */
    private generateActionId;
    /**
     * Save audit log to file
     */
    private saveAuditLog;
    /**
     * Get pending requests
     */
    getPendingRequests(): ApprovalRequest[];
    /**
     * Get audit log
     */
    getAuditLog(): SecurityAction[];
    /**
     * Update security policy
     */
    updatePolicy(updates: Partial<SecurityPolicy>): void;
    /**
     * Get current policy
     */
    getPolicy(): SecurityPolicy;
    /**
     * Clean up expired requests
     */
    cleanupExpiredRequests(): void;
}
export declare const approvalManager: ApprovalManager;
/**
 * Request approval for file operation
 */
export declare function requestFileApproval(operation: 'read' | 'write' | 'delete', filePath: string, description?: string): Promise<ApprovalRequest>;
/**
 * Request approval for command execution
 */
export declare function requestCommandApproval(command: string, args?: string[], description?: string): Promise<ApprovalRequest>;
//# sourceMappingURL=approval-system.d.ts.map