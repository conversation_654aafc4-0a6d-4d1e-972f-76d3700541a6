/**
 * Diff Overlay Component
 * 
 * Displays git diff information in a scrollable overlay
 * Shows file changes with syntax highlighting and navigation
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { getGitDiff } from '../../utils/get-diff.js';
import { logInfo, logError } from '../../utils/logger/log.js';

interface DiffOverlayProps {
  onClose: () => void;
  visible: boolean;
  workingDirectory?: string;
}

interface DiffFile {
  path: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed';
  additions: number;
  deletions: number;
  content: string;
}

export function DiffOverlay({
  onClose,
  visible,
  workingDirectory = process.cwd()
}: DiffOverlayProps) {
  const [diffContent, setDiffContent] = useState<string>('');
  const [diffFiles, setDiffFiles] = useState<DiffFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scrollOffset, setScrollOffset] = useState(0);
  const [selectedFileIndex, setSelectedFileIndex] = useState(0);
  const [viewMode, setViewMode] = useState<'summary' | 'full'>('summary');

  // Load diff when overlay becomes visible
  useEffect(() => {
    if (visible) {
      loadDiff();
    }
  }, [visible, workingDirectory]);

  // Handle keyboard input
  useInput((input, key) => {
    if (!visible) return;

    // Close overlay
    if (key.escape) {
      onClose();
      return;
    }

    // Navigation
    if (key.upArrow) {
      if (viewMode === 'summary') {
        setSelectedFileIndex(Math.max(0, selectedFileIndex - 1));
      } else {
        setScrollOffset(Math.max(0, scrollOffset - 1));
      }
      return;
    }

    if (key.downArrow) {
      if (viewMode === 'summary') {
        setSelectedFileIndex(Math.min(diffFiles.length - 1, selectedFileIndex + 1));
      } else {
        setScrollOffset(scrollOffset + 1);
      }
      return;
    }

    // Page navigation
    if (key.pageUp) {
      setScrollOffset(Math.max(0, scrollOffset - 10));
      return;
    }

    if (key.pageDown) {
      setScrollOffset(scrollOffset + 10);
      return;
    }

    // View mode toggle
    if (key.tab) {
      setViewMode(viewMode === 'summary' ? 'full' : 'summary');
      setScrollOffset(0);
      return;
    }

    // Refresh diff
    if (input === 'r' || input === 'R') {
      loadDiff();
      return;
    }

    // View specific file
    if (key.return && viewMode === 'summary' && diffFiles.length > 0) {
      setViewMode('full');
      setScrollOffset(0);
      return;
    }
  });

  /**
   * Load git diff
   */
  const loadDiff = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      logInfo('Loading git diff', { workingDirectory });
      
      const diff = await getGitDiff(workingDirectory);
      setDiffContent(diff);
      
      // Parse diff into files
      const files = parseDiffContent(diff);
      setDiffFiles(files);
      setSelectedFileIndex(0);
      
      logInfo(`Git diff loaded - ${files.length} files changed`);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load git diff';
      setError(errorMessage);
      logError('Failed to load git diff', err instanceof Error ? err : new Error(errorMessage));
    } finally {
      setLoading(false);
    }
  }, [workingDirectory]);

  /**
   * Parse diff content into structured data
   */
  const parseDiffContent = (diff: string): DiffFile[] => {
    const files: DiffFile[] = [];
    const lines = diff.split('\n');
    
    let currentFile: Partial<DiffFile> | null = null;
    let currentContent: string[] = [];

    for (const line of lines) {
      // File header
      if (line.startsWith('diff --git')) {
        // Save previous file
        if (currentFile) {
          files.push({
            ...currentFile,
            content: currentContent.join('\n')
          } as DiffFile);
        }

        // Start new file
        const match = line.match(/diff --git a\/(.+) b\/(.+)/);
        if (match) {
          currentFile = {
            path: match[1],
            status: 'modified',
            additions: 0,
            deletions: 0
          };
          currentContent = [];
        }
      }
      
      // File status
      else if (line.startsWith('new file mode')) {
        if (currentFile) currentFile.status = 'added';
      }
      else if (line.startsWith('deleted file mode')) {
        if (currentFile) currentFile.status = 'deleted';
      }
      else if (line.startsWith('rename from')) {
        if (currentFile) currentFile.status = 'renamed';
      }
      
      // Count additions/deletions
      else if (line.startsWith('+') && !line.startsWith('+++')) {
        if (currentFile) currentFile.additions = (currentFile.additions || 0) + 1;
      }
      else if (line.startsWith('-') && !line.startsWith('---')) {
        if (currentFile) currentFile.deletions = (currentFile.deletions || 0) + 1;
      }

      // Add line to content
      currentContent.push(line);
    }

    // Save last file
    if (currentFile) {
      files.push({
        ...currentFile,
        content: currentContent.join('\n')
      } as DiffFile);
    }

    return files;
  };

  /**
   * Render file summary
   */
  const renderSummary = () => {
    if (diffFiles.length === 0) {
      return (
        <Box flexDirection="column">
          <Text color="gray">No changes detected</Text>
        </Box>
      );
    }

    return (
      <Box flexDirection="column">
        <Text color="blue" bold>
          Files Changed ({diffFiles.length}):
        </Text>
        <Text></Text>
        
        {diffFiles.map((file, index) => {
          const isSelected = index === selectedFileIndex;
          const statusColor = getStatusColor(file.status);
          const statusIcon = getStatusIcon(file.status);

          return (
            <Box key={file.path}>
              <Text
                color={isSelected ? 'black' : statusColor}
                backgroundColor={isSelected ? 'cyan' : undefined}
                bold={isSelected}
              >
                {isSelected ? '► ' : '  '}
                {statusIcon} {file.path}
                {file.additions > 0 && (
                  <Text color="green"> +{file.additions}</Text>
                )}
                {file.deletions > 0 && (
                  <Text color="red"> -{file.deletions}</Text>
                )}
              </Text>
            </Box>
          );
        })}
      </Box>
    );
  };

  /**
   * Render full diff
   */
  const renderFullDiff = () => {
    if (!diffContent) {
      return (
        <Box flexDirection="column">
          <Text color="gray">No diff content available</Text>
        </Box>
      );
    }

    const lines = diffContent.split('\n');
    const visibleLines = lines.slice(scrollOffset, scrollOffset + 20);

    return (
      <Box flexDirection="column">
        {visibleLines.map((line, index) => {
          const lineNumber = scrollOffset + index + 1;
          let color = 'white';
          
          if (line.startsWith('+')) {
            color = 'green';
          } else if (line.startsWith('-')) {
            color = 'red';
          } else if (line.startsWith('@@')) {
            color = 'cyan';
          } else if (line.startsWith('diff --git')) {
            color = 'yellow';
          }

          return (
            <Text key={index} color={color}>
              {line}
            </Text>
          );
        })}
        
        {scrollOffset + 20 < lines.length && (
          <Text color="gray" dimColor>
            ... {lines.length - scrollOffset - 20} more lines
          </Text>
        )}
      </Box>
    );
  };

  /**
   * Get status color
   */
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'added': return 'green';
      case 'deleted': return 'red';
      case 'modified': return 'yellow';
      case 'renamed': return 'blue';
      default: return 'white';
    }
  };

  /**
   * Get status icon
   */
  const getStatusIcon = (status: string): string => {
    switch (status) {
      case 'added': return '+';
      case 'deleted': return '-';
      case 'modified': return '~';
      case 'renamed': return '→';
      default: return '•';
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <Box
      position="absolute"
      borderStyle="double"
      borderColor="cyan"
      flexDirection="column"
      width="90%"
      height="80%"
    >
      {/* Header */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="cyan" bold>
          Git Diff - {viewMode === 'summary' ? 'Summary' : 'Full Diff'}
        </Text>
        <Box>
          <Text color="gray">
            Tab: Toggle view • ↑↓: Navigate • R: Refresh • Esc: Close
          </Text>
        </Box>
      </Box>

      {/* Content */}
      <Box flexGrow={1} paddingX={2} paddingY={1}>
        {loading ? (
          <Text color="yellow">Loading git diff...</Text>
        ) : error ? (
          <Box flexDirection="column">
            <Text color="red">Error: {error}</Text>
            <Text color="gray">Press R to retry</Text>
          </Box>
        ) : viewMode === 'summary' ? (
          renderSummary()
        ) : (
          renderFullDiff()
        )}
      </Box>

      {/* Footer */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="gray">
          Working Directory: {workingDirectory}
        </Text>
      </Box>
    </Box>
  );
}
