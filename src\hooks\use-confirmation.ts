/**
 * Confirmation Queue Management Hook
 * 
 * Manages multiple confirmation requests with promise-based API
 * Provides queue-based system for handling confirmations
 */

import React, { useState, useCallback, useRef } from 'react';

export interface ConfirmationResult {
  confirmed: boolean;
  value?: string;
  action?: string;
}

interface ConfirmationRequest {
  id: string;
  prompt: React.ReactElement;
  explanation?: string;
  resolve: (result: ConfirmationResult) => void;
  reject: (error: Error) => void;
}

/**
 * Custom hook for managing confirmation dialogs
 */
export function useConfirmation(): {
  submitConfirmation: (result: ConfirmationResult) => void;
  requestConfirmation: (
    prompt: React.ReactElement,
    explanation?: string,
  ) => Promise<ConfirmationResult>;
  confirmationPrompt: React.ReactElement | null;
  explanation?: string;
  isConfirmationPending: boolean;
} {
  const [currentRequest, setCurrentRequest] = useState<ConfirmationRequest | null>(null);
  // Queue for future multi-confirmation support
  const [_queue, _setQueue] = useState<ConfirmationRequest[]>([]);
  const requestIdRef = useRef(0);

  /**
   * Submit confirmation result
   */
  const submitConfirmation = useCallback((result: ConfirmationResult) => {
    if (currentRequest) {
      currentRequest.resolve(result);
      setCurrentRequest(null);
      
      // Process next item in queue
      setQueue(prevQueue => {
        const [nextRequest, ...remainingQueue] = prevQueue;
        if (nextRequest) {
          setCurrentRequest(nextRequest);
        }
        return remainingQueue;
      });
    }
  }, [currentRequest]);

  /**
   * Request confirmation with promise-based API
   */
  const requestConfirmation = useCallback((
    prompt: React.ReactElement,
    explanation?: string,
  ): Promise<ConfirmationResult> => {
    return new Promise((resolve, reject) => {
      const id = `confirmation-${++requestIdRef.current}`;
      const request: ConfirmationRequest = {
        id,
        prompt,
        explanation,
        resolve,
        reject,
      };

      if (currentRequest) {
        // Add to queue if there's already a pending confirmation
        setQueue(prevQueue => [...prevQueue, request]);
      } else {
        // Set as current request if no pending confirmation
        setCurrentRequest(request);
      }
    });
  }, [currentRequest]);

  return {
    submitConfirmation,
    requestConfirmation,
    confirmationPrompt: currentRequest?.prompt || null,
    explanation: currentRequest?.explanation,
    isConfirmationPending: currentRequest !== null,
  };
}
