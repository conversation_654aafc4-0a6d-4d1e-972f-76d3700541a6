{"version": 3, "file": "approval-system.js", "sourceRoot": "", "sources": ["../../../src/utils/security/approval-system.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAE5B,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAiD9D;;GAEG;AACH,MAAM,OAAO,eAAe;IAClB,eAAe,GAAG,IAAI,GAAG,EAA2B,CAAC;IACrD,QAAQ,GAAqB,EAAE,CAAC;IAChC,MAAM,CAAiB;IAE/B,YAAY,MAAgC;QAC1C,IAAI,CAAC,MAAM,GAAG;YACZ,WAAW,EAAE;gBACX,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,KAAK;aACpB;YACD,iBAAiB,EAAE;gBACjB,GAAG,EAAE,CAAC;gBACN,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,CAAC;gBACP,QAAQ,EAAE,CAAC;aACZ;YACD,eAAe,EAAE,EAAE;YACnB,YAAY,EAAE,IAAI;YAClB,eAAe,EAAE;gBACf,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,mBAAmB;aACpB;YACD,eAAe,EAAE;gBACf,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI;gBAC/C,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK;aAC9C;YACD,eAAe,EAAE;gBACf,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM;gBACtC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;aAC/B;YACD,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,MAAsB;QAC1C,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC1E,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;QAE/F,MAAM,OAAO,GAAoB;YAC/B,EAAE,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC5B,MAAM;YACN,iBAAiB;YACjB,SAAS,EAAE,EAAE;YACb,MAAM,EAAE,SAAS;YACjB,SAAS;YACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,wDAAwD;QACxD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7C,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC;YAC5B,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;gBACrB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,SAAS;gBACnB,MAAM,EAAE,+BAA+B;gBACvC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAChD,CAAC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3B,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC5B,CAAC;QAED,OAAO,CAAC,oBAAoB,OAAO,CAAC,EAAE,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;QAC5F,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,SAAiB,EACjB,MAAc,EACd,QAA8B,EAC9B,MAAe;QAEf,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,oBAAoB,SAAS,YAAY,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,uBAAuB,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7C,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;YAC3B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,cAAc,CAAC,CAAC;QACtD,CAAC;QAED,0CAA0C;QAC1C,MAAM,gBAAgB,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QAC1E,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,QAAQ,MAAM,8CAA8C,SAAS,EAAE,CAAC,CAAC;QAC3F,CAAC;QAED,eAAe;QACf,MAAM,QAAQ,GAAa;YACzB,MAAM;YACN,QAAQ;YACR,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEjC,+CAA+C;QAC/C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QACjF,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;QAEjF,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACnB,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC;YAC5B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,SAAS,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAClD,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC;YAC5B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,CAAC,iCAAiC,SAAS,KAAK,QAAQ,OAAO,MAAM,EAAE,CAAC,CAAC;QAChF,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,MAA8D;QACvE,kBAAkB;QAClB,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzC,OAAO,UAAU,CAAC;YACpB,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzC,OAAO,MAAM,CAAC;YAChB,CAAC;YACD,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvE,OAAO,QAAQ,CAAC;YAClB,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,MAAM,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5C,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClD,OAAO,UAAU,CAAC;YACpB,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,mBAAmB;QACnB,IAAI,MAAM,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACtC,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/E,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,uBAAuB;QACvB,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YACpC,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAClB,IAA4B,EAC5B,WAAmB,EACnB,MAAc,EACd,WAAgC,EAAE;QAElC,MAAM,MAAM,GAA2D;YACrE,IAAI;YACJ,WAAW;YACX,MAAM;YACN,QAAQ;SACT,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAE1C,OAAO;YACL,GAAG,MAAM;YACT,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAC3B,SAAS;YACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,IAAY;QACnC,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CACnD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,SAAiD;QACzE,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC;YACnD,KAAK,QAAQ,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;YACzD,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACrD,KAAK,UAAU,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC;YAC7D,OAAO,CAAC,CAAC,OAAO,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvD,OAAO,OAAO,SAAS,IAAI,MAAM,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvD,OAAO,OAAO,SAAS,IAAI,MAAM,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY;QACxB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,sBAAsB,CAAC,CAAC;YAC5D,MAAM,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,0BAA0B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAClG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,OAAgC;QAC3C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC;QAC7C,OAAO,CAAC,yBAAyB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACjD,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC;gBACtC,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;gBAC3B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAChC,OAAO,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAED,mCAAmC;AACnC,MAAM,CAAC,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,SAAsC,EACtC,QAAgB,EAChB,WAAoB;IAEpB,MAAM,UAAU,GAAG,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC;IACzE,MAAM,iBAAiB,GAAG,WAAW,IAAI,GAAG,SAAS,UAAU,QAAQ,EAAE,CAAC;IAE1E,MAAM,MAAM,GAAG,eAAe,CAAC,oBAAoB,CACjD,UAAU,EACV,iBAAiB,EACjB,QAAQ,EACR,EAAE,SAAS,EAAE,CACd,CAAC;IAEF,OAAO,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AACjD,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB,CAC1C,OAAe,EACf,OAAiB,EAAE,EACnB,WAAoB;IAEpB,MAAM,WAAW,GAAG,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;IAC1D,MAAM,iBAAiB,GAAG,WAAW,IAAI,oBAAoB,WAAW,EAAE,CAAC;IAE3E,MAAM,MAAM,GAAG,eAAe,CAAC,oBAAoB,CACjD,iBAAiB,EACjB,iBAAiB,EACjB,WAAW,EACX,EAAE,OAAO,EAAE,IAAI,EAAE,CAClB,CAAC;IAEF,OAAO,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AACjD,CAAC"}