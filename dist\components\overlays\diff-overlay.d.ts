/**
 * Diff Overlay Component
 *
 * Displays git diff information in a scrollable overlay
 * Shows file changes with syntax highlighting and navigation
 */
interface DiffOverlayProps {
    onClose: () => void;
    visible: boolean;
    workingDirectory?: string;
}
export declare function DiffOverlay({ onClose, visible, workingDirectory }: DiffOverlayProps): import("react/jsx-runtime").JSX.Element | null;
export {};
//# sourceMappingURL=diff-overlay.d.ts.map