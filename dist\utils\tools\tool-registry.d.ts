/**
 * Comprehensive Tool Registry System
 *
 * Manages all available tools, their execution, and integration
 * Provides tool discovery, validation, and execution framework
 */
export interface Tool {
    id: string;
    name: string;
    description: string;
    category: 'file' | 'git' | 'system' | 'network' | 'analysis' | 'development';
    version: string;
    parameters: ToolParameter[];
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    requiresApproval: boolean;
    execute: (params: Record<string, any>, context: ToolContext) => Promise<ToolResult>;
}
export interface ToolParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    description: string;
    required: boolean;
    default?: any;
    validation?: (value: any) => boolean;
}
export interface ToolContext {
    workingDirectory: string;
    userId?: string;
    sessionId?: string;
    environment: Record<string, string>;
    capabilities: string[];
}
export interface ToolResult {
    success: boolean;
    data?: any;
    output?: string;
    error?: string;
    metadata?: Record<string, any>;
    executionTime: number;
}
/**
 * Tool registry and execution manager
 */
export declare class ToolRegistry {
    private tools;
    private executionHistory;
    constructor();
    /**
     * Register a tool
     */
    registerTool(tool: Tool): void;
    /**
     * Get tool by ID
     */
    getTool(id: string): Tool | undefined;
    /**
     * Get all tools
     */
    getAllTools(): Tool[];
    /**
     * Get tools by category
     */
    getToolsByCategory(category: Tool['category']): Tool[];
    /**
     * Execute tool with approval and security checks
     */
    executeTool(toolId: string, parameters: Record<string, any>, context: ToolContext): Promise<ToolResult>;
    /**
     * Validate tool parameters
     */
    private validateParameters;
    /**
     * Validate parameter type
     */
    private validateParameterType;
    /**
     * Register built-in tools
     */
    private registerBuiltinTools;
    /**
     * Get execution history
     */
    getExecutionHistory(): ToolExecution[];
    /**
     * Clear execution history
     */
    clearExecutionHistory(): void;
    /**
     * Get tool usage statistics
     */
    getUsageStatistics(): Record<string, number>;
}
interface ToolExecution {
    toolId: string;
    parameters: Record<string, any>;
    result: ToolResult;
    context: ToolContext;
    timestamp: string;
    executionTime: number;
}
export declare const toolRegistry: ToolRegistry;
/**
 * Execute tool by ID
 */
export declare function executeTool(toolId: string, parameters?: Record<string, any>, context?: Partial<ToolContext>): Promise<ToolResult>;
/**
 * Get available tools
 */
export declare function getAvailableTools(): Tool[];
export {};
//# sourceMappingURL=tool-registry.d.ts.map