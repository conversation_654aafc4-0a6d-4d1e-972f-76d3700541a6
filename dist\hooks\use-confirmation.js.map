{"version": 3, "file": "use-confirmation.js", "sourceRoot": "", "sources": ["../../src/hooks/use-confirmation.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAgB7D;;GAEG;AACH,MAAM,UAAU,eAAe;IAU7B,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAA6B,IAAI,CAAC,CAAC;IACvF,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAwB,EAAE,CAAC,CAAC;IAC9D,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAE/B;;OAEG;IACH,MAAM,kBAAkB,GAAG,WAAW,CAAC,CAAC,MAA0B,EAAE,EAAE;QACpE,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC/B,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExB,6BAA6B;YAC7B,QAAQ,CAAC,SAAS,CAAC,EAAE;gBACnB,MAAM,CAAC,WAAW,EAAE,GAAG,cAAc,CAAC,GAAG,SAAS,CAAC;gBACnD,IAAI,WAAW,EAAE,CAAC;oBAChB,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBACjC,CAAC;gBACD,OAAO,cAAc,CAAC;YACxB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;IAErB;;OAEG;IACH,MAAM,mBAAmB,GAAG,WAAW,CAAC,CACtC,MAA0B,EAC1B,WAAoB,EACS,EAAE;QAC/B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,OAAO,EAAE,CAAC;YACpD,MAAM,OAAO,GAAwB;gBACnC,EAAE;gBACF,MAAM;gBACN,WAAW;gBACX,OAAO;gBACP,MAAM;aACP,CAAC;YAEF,IAAI,cAAc,EAAE,CAAC;gBACnB,yDAAyD;gBACzD,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,oDAAoD;gBACpD,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;IAErB,OAAO;QACL,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB,EAAE,cAAc,EAAE,MAAM,IAAI,IAAI;QAClD,WAAW,EAAE,cAAc,EAAE,WAAW;QACxC,qBAAqB,EAAE,cAAc,KAAK,IAAI;KAC/C,CAAC;AACJ,CAAC"}