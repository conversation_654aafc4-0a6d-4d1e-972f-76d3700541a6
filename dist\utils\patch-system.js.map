{"version": 3, "file": "patch-system.js", "sourceRoot": "", "sources": ["../../src/utils/patch-system.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,IAAI,EAAW,QAAQ,EAAE,MAAM,MAAM,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AACpC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAuC7D;;GAEG;AACH,MAAM,OAAO,YAAY;IACf,cAAc,GAAG,IAAI,GAAG,EAAiB,CAAC;IAC1C,eAAe,CAAS;IAEhC,YAAY,kBAA0B,UAAU;QAC9C,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,YAAoB,EACpB,YAAoB,EACpB,cAAsB,sBAAsB;QAE5C,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YACjE,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAEjE,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;YACzF,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEpD,MAAM,KAAK,GAAU;gBACnB,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE;gBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW;gBACX,UAAU;gBACV,QAAQ;gBACR,QAAQ,EAAE,EAAE;aACb,CAAC;YAEF,OAAO,CAAC,mBAAmB,KAAK,CAAC,EAAE,SAAS,UAAU,CAAC,MAAM,aAAa,CAAC,CAAC;YAC5E,OAAO,KAAK,CAAC;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,0BAA0B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChG,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,KAAY,EAAE,SAAkB,KAAK;QACpD,MAAM,MAAM,GAAgB;YAC1B,OAAO,EAAE,IAAI;YACb,iBAAiB,EAAE,CAAC;YACpB,gBAAgB,EAAE,EAAE;YACpB,SAAS,EAAE,EAAE;YACb,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,0BAA0B;QAC1B,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACpE,IAAI,kBAAkB,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAC3E,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrC,CAAC;QAED,uBAAuB;QACvB,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACzC,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBAErE,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC;oBAC5B,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACxC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;oBACpD,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACzB,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACxC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACvB,QAAQ,CAAC,gCAAgC,SAAS,CAAC,IAAI,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACxH,CAAC;QACH,CAAC;QAED,4CAA4C;QAC5C,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE,aAAa,MAAM,CAAC,iBAAiB,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,wBAAwB,CAAC,CAAC;QACnH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAC1B,SAAyB,EACzB,MAAe;QAEf,MAAM,SAAS,GAAoB,EAAE,CAAC;QAEtC,IAAI,CAAC;YACH,4BAA4B;YAC5B,IAAI,cAAc,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC;gBACH,cAAc,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC9D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;oBAC7B,MAAM,IAAI,KAAK,CAAC,QAAQ,SAAS,CAAC,IAAI,iBAAiB,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;YAED,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEzC,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;gBACvB,KAAK,KAAK;oBACR,IAAI,CAAC,MAAM,EAAE,CAAC;wBACZ,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBACtC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;wBACzD,MAAM,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,CAAC;oBACD,MAAM;gBAER,KAAK,QAAQ;oBACX,IAAI,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;wBACzC,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,SAAS,CAAC,IAAI;4BACpB,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,QAAQ,EAAE,SAAS,CAAC,eAAe,IAAI,EAAE;4BACzC,MAAM,EAAE,KAAK;4BACb,SAAS;yBACV,CAAC,CAAC;wBACH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;oBACvC,CAAC;oBAED,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;oBACjD,IAAI,SAAS,CAAC,eAAe,IAAI,YAAY,KAAK,SAAS,CAAC,eAAe,EAAE,CAAC;wBAC5E,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,SAAS,CAAC,IAAI;4BACpB,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,QAAQ,EAAE,SAAS,CAAC,eAAe;4BACnC,MAAM,EAAE,YAAY;4BACpB,SAAS;yBACV,CAAC,CAAC;wBACH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;oBACvC,CAAC;oBAED,IAAI,CAAC,MAAM,EAAE,CAAC;wBACZ,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBACtC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;wBACtC,MAAM,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,CAAC;oBACD,MAAM;gBAER,KAAK,QAAQ;oBACX,IAAI,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;wBACzC,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,SAAS,CAAC,IAAI;4BACpB,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,QAAQ,EAAE,SAAS,CAAC,eAAe,IAAI,EAAE;4BACzC,MAAM,EAAE,KAAK;4BACb,SAAS;yBACV,CAAC,CAAC;wBACH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;oBACvC,CAAC;oBAED,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;oBACjD,IAAI,SAAS,CAAC,eAAe,IAAI,YAAY,KAAK,SAAS,CAAC,eAAe,EAAE,CAAC;wBAC5E,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,SAAS,CAAC,IAAI;4BACpB,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,QAAQ,EAAE,SAAS,CAAC,eAAe;4BACnC,MAAM,EAAE,YAAY;4BACpB,SAAS;yBACV,CAAC,CAAC;wBACH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;oBACvC,CAAC;oBAED,IAAI,CAAC,MAAM,EAAE,CAAC;wBACZ,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBACtC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;wBAChD,MAAM,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,CAAC;oBACD,MAAM;YACV,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;QAE1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,mBAAmB,SAAS,CAAC,IAAI,YAAY,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACnH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,OAAe;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,SAAS,OAAO,YAAY,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC;YACH,4BAA4B;YAC5B,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBACzC,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEtC,OAAO,CAAC,SAAS,OAAO,2BAA2B,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,4BAA4B,OAAO,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3G,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB,EAAE,QAAgB,EAAE,QAAgB;QAC3E,MAAM,UAAU,GAAqB,EAAE,CAAC;QACxC,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE3C,kFAAkF;QAClF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;QAEtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;gBAC7D,aAAa;gBACb,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,CAAC;oBACb,OAAO,EAAE,YAAY;iBACtB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;gBACpE,eAAe;gBACf,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,CAAC;oBACb,OAAO,EAAE,EAAE;oBACX,eAAe,EAAE,YAAY;iBAC9B,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,YAAY,KAAK,YAAY,EAAE,CAAC;gBACzC,gBAAgB;gBAChB,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,CAAC;oBACb,OAAO,EAAE,YAAY;oBACrB,eAAe,EAAE,YAAY;iBAC9B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,UAA4B;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACpD,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvD,OAAO,SAAS,SAAS,IAAI,MAAM,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC9E,MAAM,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,oDAAoD;YACpD,IAAK,KAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACrC,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,OAAe;QACzD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAE9E,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACvD,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,qBAAqB,QAAQ,cAAc,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gCAAgC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,KAAY;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;QAClE,MAAM,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,OAAO,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAuB;QACzB,CAAC;IACH,CAAC;CACF;AAED,gCAAgC;AAChC,MAAM,CAAC,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC"}