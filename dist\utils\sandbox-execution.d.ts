/**
 * Sandbox Execution System
 *
 * Provides secure execution environment for code and commands
 * Includes timeout handling, resource limits, and security controls
 */
export interface SandboxOptions {
    timeout?: number;
    maxMemory?: number;
    maxCpu?: number;
    workingDirectory?: string;
    environment?: Record<string, string>;
    allowNetworking?: boolean;
    allowFileSystem?: boolean;
    restrictedPaths?: string[];
}
export interface ExecutionResult {
    success: boolean;
    stdout: string;
    stderr: string;
    exitCode: number | null;
    signal: string | null;
    duration: number;
    memoryUsage?: number;
    error?: string;
}
export interface SandboxEnvironment {
    id: string;
    workingDirectory: string;
    cleanup: () => Promise<void>;
}
/**
 * Sandbox execution manager
 */
export declare class SandboxExecutor {
    private activeProcesses;
    private environments;
    /**
     * Create a new sandbox environment
     */
    createEnvironment(options?: SandboxOptions): Promise<SandboxEnvironment>;
    /**
     * Execute command in sandbox
     */
    executeCommand(command: string, args?: string[], options?: SandboxOptions): Promise<ExecutionResult>;
    /**
     * Execute JavaScript code in sandbox
     */
    executeJavaScript(code: string, options?: SandboxOptions): Promise<ExecutionResult>;
    /**
     * Execute Python code in sandbox
     */
    executePython(code: string, options?: SandboxOptions): Promise<ExecutionResult>;
    /**
     * Execute shell script in sandbox
     */
    executeShell(script: string, options?: SandboxOptions): Promise<ExecutionResult>;
    /**
     * Kill all active processes
     */
    killAllProcesses(): void;
    /**
     * Cleanup all environments
     */
    cleanup(): Promise<void>;
    /**
     * Get active process count
     */
    getActiveProcessCount(): number;
    /**
     * Get environment count
     */
    getEnvironmentCount(): number;
}
export declare const sandboxExecutor: SandboxExecutor;
/**
 * Execute code with automatic language detection
 */
export declare function executeCode(code: string, language?: string, options?: SandboxOptions): Promise<ExecutionResult>;
/**
 * Validate sandbox options
 */
export declare function validateSandboxOptions(options: SandboxOptions): string[];
/**
 * Create secure sandbox with resource limits
 */
export declare function createSecureSandbox(options?: SandboxOptions): Promise<SandboxEnvironment>;
/**
 * Execute with resource monitoring
 */
export declare function executeWithMonitoring(command: string, args?: string[], options?: SandboxOptions): Promise<ExecutionResult & {
    resourceUsage: any;
}>;
//# sourceMappingURL=sandbox-execution.d.ts.map