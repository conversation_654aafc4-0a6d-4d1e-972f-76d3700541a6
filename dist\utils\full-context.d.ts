/**
 * Full-Context Mode System
 *
 * Provides comprehensive context gathering and analysis
 * Includes file analysis, dependency mapping, and intelligent context selection
 */
export interface ContextFile {
    path: string;
    relativePath: string;
    size: number;
    type: 'source' | 'config' | 'documentation' | 'data' | 'other';
    language?: string;
    importance: number;
    content?: string;
    summary?: string;
    dependencies?: string[];
    exports?: string[];
    imports?: string[];
}
export interface ProjectContext {
    rootPath: string;
    files: ContextFile[];
    structure: ProjectStructure;
    dependencies: DependencyMap;
    gitInfo: any;
    metadata: {
        totalFiles: number;
        totalSize: number;
        languages: string[];
        frameworks: string[];
        buildTools: string[];
    };
}
export interface ProjectStructure {
    directories: string[];
    filesByType: Record<string, string[]>;
    configFiles: string[];
    entryPoints: string[];
}
export interface DependencyMap {
    internal: Record<string, string[]>;
    external: Record<string, string[]>;
    circular: string[][];
}
/**
 * Full context analyzer
 */
export declare class FullContextAnalyzer {
    private maxFileSize;
    private maxTotalSize;
    private excludePatterns;
    /**
     * Analyze project and gather full context
     */
    analyzeProject(rootPath?: string): Promise<ProjectContext>;
    /**
     * Discover files in project
     */
    private discoverFiles;
    /**
     * Analyze individual files
     */
    private analyzeFiles;
    /**
     * Determine file type
     */
    private determineFileType;
    /**
     * Detect programming language
     */
    private detectLanguage;
    /**
     * Calculate file importance
     */
    private calculateImportance;
    /**
     * Generate file summary
     */
    private generateSummary;
    /**
     * Analyze source code for dependencies and exports
     */
    private analyzeSourceCode;
    /**
     * Build project structure
     */
    private buildProjectStructure;
    /**
     * Map dependencies between files
     */
    private mapDependencies;
    /**
     * Calculate project metadata
     */
    private calculateMetadata;
}
export declare const fullContextAnalyzer: FullContextAnalyzer;
export declare function getProjectContext(rootPath?: string, forceRefresh?: boolean): Promise<ProjectContext>;
//# sourceMappingURL=full-context.d.ts.map